'use client'

import { useEffect } from 'react'

export default function DynamicCSS() {
  useEffect(() => {
    // Create and append the preload link for optimized icons
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = '/optimized-icons.css'
    link.as = 'style'
    link.onload = () => {
      link.onload = null
      link.rel = 'stylesheet'
    }
    
    // Check if the link doesn't already exist
    const existingLink = document.querySelector('link[href="/optimized-icons.css"]')
    if (!existingLink) {
      document.head.appendChild(link)
    }
  }, [])

  return null
}
