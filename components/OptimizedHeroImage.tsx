'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

interface OptimizedHeroImageProps {
  src: string;
  alt: string;
  priority?: boolean;
  className?: string;
  onLoad?: () => void;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
}

/**
 * مكون صورة محسن خصيصاً للهيرو مع تحميل فوري لتحسين LCP
 */
const OptimizedHeroImage: React.FC<OptimizedHeroImageProps> = ({
  src,
  alt,
  priority = false,
  className = '',
  onLoad,
  onError
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);

  // تحديث المصدر عند تغيير src
  useEffect(() => {
    setImageSrc(src);
    setHasError(false);
    setIsLoading(true);
  }, [src]);

  // معالج تحميل الصورة
  const handleLoad = () => {
    setIsLoading(false);
    if (onLoad) onLoad();
  };

  // معالج خطأ الصورة
  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setHasError(true);
    setIsLoading(false);
    if (onError) onError(e);
  };

  // إنشاء blur data URL
  const blurDataURL = `data:image/svg+xml;base64,${Buffer.from(
    `<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" />
      <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="rgba(255,255,255,0.8)" font-size="48" font-family="Arial">DROOB HAJER</text>
    </svg>`
  ).toString('base64')}`;

  return (
    <div className="relative w-full h-full">
      <Image
        src={imageSrc}
        alt={alt}
        fill
        className={`object-cover transition-opacity duration-500 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        } ${className}`}
        priority={priority}
        quality={priority ? 95 : 80}
        sizes="100vw"
        placeholder="blur"
        blurDataURL={blurDataURL}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          objectFit: 'cover',
          objectPosition: 'center',
        }}
      />
      
      {/* Loading overlay */}
      {isLoading && !hasError && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg font-medium">جاري التحميل...</p>
          </div>
        </div>
      )}
      
      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-500 to-gray-700 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
              <i className="ri-image-line text-2xl"></i>
            </div>
            <p className="text-lg font-medium">خطأ في تحميل الصورة</p>
            <p className="text-sm opacity-80">يرجى المحاولة مرة أخرى</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedHeroImage;
