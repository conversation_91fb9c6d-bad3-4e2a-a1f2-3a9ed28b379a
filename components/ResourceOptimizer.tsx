'use client';

import { useEffect } from 'react';

/**
 * مكون لتحسين تحميل الموارد وتقليل وقت LCP
 */
const ResourceOptimizer: React.FC = () => {
  useEffect(() => {
    // تحسين تحميل الصور
    const optimizeImages = () => {
      // إضافة preload للصور المهمة
      const heroImages = document.querySelectorAll('[data-hero-image]');
      heroImages.forEach((img, index) => {
        if (index === 0) { // الصورة الأولى فقط
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'image';
          link.href = (img as HTMLImageElement).src;
          document.head.appendChild(link);
        }
      });
    };

    // تحسين تحميل الخطوط
    const optimizeFonts = () => {
      // preload للخطوط المهمة
      const fontPreloads = [
        {
          href: 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2',
          type: 'font/woff2'
        },
        {
          href: 'https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJfecg.woff2',
          type: 'font/woff2'
        }
      ];

      fontPreloads.forEach(font => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = font.type;
        link.href = font.href;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });
    };

    // تحسين CSS
    const optimizeCSS = () => {
      // تحميل CSS غير الحرج بعد تحميل الصفحة
      const nonCriticalCSS = [
        'https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css'
      ];

      nonCriticalCSS.forEach(href => {
        const existingLink = document.querySelector(`link[href="${href}"]`);
        if (!existingLink) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = href;
          link.media = 'print';
          link.onload = function() {
            (this as HTMLLinkElement).media = 'all';
          };
          document.head.appendChild(link);
        }
      });
    };

    // تحسين JavaScript
    const optimizeJS = () => {
      // تأجيل تحميل JavaScript غير الحرج
      const scripts = document.querySelectorAll('script[data-defer]');
      scripts.forEach(script => {
        const newScript = document.createElement('script');
        newScript.src = script.getAttribute('src') || '';
        newScript.async = true;
        newScript.defer = true;
        document.head.appendChild(newScript);
      });
    };

    // تحسين الاتصالات
    const optimizeConnections = () => {
      const preconnectDomains = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://cdn.jsdelivr.net',
        'https://images.unsplash.com'
      ];

      preconnectDomains.forEach(domain => {
        const existingLink = document.querySelector(`link[href="${domain}"]`);
        if (!existingLink) {
          const link = document.createElement('link');
          link.rel = 'preconnect';
          link.href = domain;
          if (domain.includes('fonts.gstatic.com')) {
            link.crossOrigin = 'anonymous';
          }
          document.head.appendChild(link);
        }
      });
    };

    // تحسين الذاكرة
    const optimizeMemory = () => {
      // تنظيف event listeners غير المستخدمة
      const unusedListeners = document.querySelectorAll('[data-cleanup]');
      unusedListeners.forEach(element => {
        // إزالة event listeners قديمة
        element.removeEventListener('click', () => {});
        element.removeEventListener('scroll', () => {});
      });

      // تنظيف timers
      const timers = (window as Window & { __timers?: number[] }).__timers || [];
      timers.forEach((timer: number) => {
        clearTimeout(timer);
        clearInterval(timer);
      });
    };

    // تحسين التمرير
    const optimizeScrolling = () => {
      let ticking = false;

      const updateScrollPosition = () => {
        // منطق تحديث موقع التمرير
        ticking = false;
      };

      const onScroll = () => {
        if (!ticking) {
          requestAnimationFrame(updateScrollPosition);
          ticking = true;
        }
      };

      window.addEventListener('scroll', onScroll, { passive: true });

      return () => {
        window.removeEventListener('scroll', onScroll);
      };
    };

    // تحسين الصور الكسولة
    const optimizeLazyImages = () => {
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              const src = img.dataset.src;
              if (src) {
                img.src = src;
                img.removeAttribute('data-src');
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
              }
            }
          });
        }, {
          rootMargin: '50px 0px',
          threshold: 0.01
        });

        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => imageObserver.observe(img));
      }
    };

    // تحسين Web Vitals
    const optimizeWebVitals = () => {
      // تحسين CLS
      const preventLayoutShift = () => {
        const images = document.querySelectorAll('img:not([width]):not([height])');
        images.forEach(img => {
          (img as HTMLImageElement).style.aspectRatio = '16/9'; // نسبة افتراضية
        });
      };

      // تحسين FID
      const optimizeInteractivity = () => {
        // تأجيل JavaScript غير الحرج
        const deferredTasks: (() => void)[] = [];
        
        const runDeferredTasks = () => {
          while (deferredTasks.length > 0) {
            const task = deferredTasks.shift();
            if (task) task();
          }
        };

        // تشغيل المهام المؤجلة عند الخمول
        if ('requestIdleCallback' in window) {
          requestIdleCallback(runDeferredTasks);
        } else {
          setTimeout(runDeferredTasks, 1);
        }
      };

      preventLayoutShift();
      optimizeInteractivity();
    };

    // تشغيل التحسينات
    const runOptimizations = () => {
      optimizeConnections();
      optimizeFonts();
      optimizeImages();
      optimizeCSS();
      optimizeJS();
      optimizeLazyImages();
      optimizeWebVitals();
      
      const scrollCleanup = optimizeScrolling();
      
      // تنظيف الذاكرة بعد 5 ثوان
      setTimeout(optimizeMemory, 5000);

      return scrollCleanup;
    };

    // تشغيل التحسينات عند تحميل الصفحة
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', runOptimizations);
    } else {
      const cleanup = runOptimizations();
      return cleanup;
    }
  }, []);

  return null; // هذا المكون لا يعرض أي محتوى
};

export default ResourceOptimizer;
