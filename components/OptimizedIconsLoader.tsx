'use client'

import { useEffect } from 'react'

export default function OptimizedIconsLoader() {
  useEffect(() => {
    // Create preload link for optimized icons
    const preloadLink = document.createElement('link')
    preloadLink.rel = 'preload'
    preloadLink.href = '/optimized-icons.css'
    preloadLink.as = 'style'
    
    // Convert to stylesheet when loaded
    preloadLink.onload = () => {
      preloadLink.onload = null
      preloadLink.rel = 'stylesheet'
    }
    
    // Check if already exists
    const existingLink = document.querySelector('link[href="/optimized-icons.css"]')
    if (!existingLink) {
      document.head.appendChild(preloadLink)
    }
  }, [])

  return null
}
